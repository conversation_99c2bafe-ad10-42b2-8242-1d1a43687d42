package com.jxhy.partr;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Display;
import com.google.mediapipe.framework.image.BitmapImageBuilder;
import com.google.mediapipe.framework.image.MPImage;
import com.google.mediapipe.tasks.components.containers.NormalizedLandmark;
import com.google.mediapipe.tasks.core.BaseOptions;
import com.google.mediapipe.tasks.core.Delegate;
import com.google.mediapipe.tasks.core.OutputHandler;
import com.google.mediapipe.tasks.vision.core.RunningMode;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerResult;
import df.a;
import dh.e;
import ef.a;
import ef.c;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import kg.g;
import kg.l;
import kg.o;
import lf.i;
import lf.j;
import lg.b0;
import ug.b;
import vd.b;
import xg.g;
import xg.m;

public final class PoseLandmarkerPlugin implements a, j.c, a {
  public static final String CHANNEL_NAME = "pose_landmarker_plugin";
  
  public static final a Companion = new a(null);
  
  public static final String TAG = "PoseLandmarker";
  
  private final long SAVE_INTERVAL = 10000L;
  
  private Activity activity;
  
  private j channel;
  
  private Context context;
  
  private long lastSaveTime;
  
  private PoseLandmarker poseLandmarker;
  
  private final void convertRGBAToARGB(byte[] paramArrayOfbyte, int[] paramArrayOfint, int paramInt) {
    for (byte b = 0; b < paramInt; b++) {
      int i = b * 4;
      byte b1 = paramArrayOfbyte[i];
      byte b2 = paramArrayOfbyte[i + 1];
      byte b3 = paramArrayOfbyte[i + 2];
      paramArrayOfint[b] = (paramArrayOfbyte[i + 3] & 0xFF) << 24 | (b1 & 0xFF) << 16 | (b2 & 0xFF) << 8 | b3 & 0xFF;
    } 
  }
  
  private final String copyAssetToCache(Context paramContext, String paramString) {
    try {
      String str2;
      String str1;
      File file = new File();
      this(paramContext.getCacheDir(), paramString);
      boolean bool = file.exists();
      if (bool) {
        long l = paramContext.getAssets().openFd(paramString).getLength();
        if (file.length() == l) {
          str2 = file.getAbsolutePath();
          m.d(str2, "getAbsolutePath(...)");
          return str2;
        } 
      } 
      InputStream inputStream = str2.getAssets().open(paramString);
      try {
        FileOutputStream fileOutputStream = new FileOutputStream();
        this(file);
      } finally {
        paramString = null;
      } 
    } catch (Exception exception) {
      Log.e("PoseLandmarker", "复制模型文件到缓存失败", exception);
      throw exception;
    } 
  }
  
  private final int getDeviceOrientation() {
    Activity activity = this.activity;
    if (activity != null) {
      boolean bool;
      Display display = pd.a.a(activity);
      char c2 = Character.MIN_VALUE;
      if (display != null) {
        bool = display.getRotation();
      } else {
        bool = false;
      } 
      char c1 = c2;
      if (bool)
        if (bool != true) {
          if (bool != 2) {
            if (bool != 3) {
              c1 = c2;
            } else {
              c1 = 'Ď';
            } 
          } else {
            c1 = '´';
          } 
        } else {
          c1 = 'Z';
        }  
      return c1;
    } 
    throw new IllegalStateException("Activity not available");
  }
  
  private static final void handleResult$lambda$1(PoseLandmarkerPlugin paramPoseLandmarkerPlugin, List paramList) {
    m.e(paramPoseLandmarkerPlugin, "this$0");
    m.e(paramList, "$poses");
    j j2 = paramPoseLandmarkerPlugin.channel;
    j j1 = j2;
    if (j2 == null) {
      m.n("channel");
      j1 = null;
    } 
    j1.c("onResult", paramList);
  }
  
  private static final void initialize$lambda$0(PoseLandmarkerPlugin paramPoseLandmarkerPlugin, PoseLandmarkerResult paramPoseLandmarkerResult, MPImage paramMPImage) {
    m.e(paramPoseLandmarkerPlugin, "this$0");
    m.e(paramPoseLandmarkerResult, "poseResult");
    m.e(paramMPImage, "image");
    paramPoseLandmarkerPlugin.handleResult(paramPoseLandmarkerResult);
  }
  
  public final void convertYUVToRGB(byte[] paramArrayOfbyte, int paramInt1, int paramInt2, int[] paramArrayOfint) {
    m.e(paramArrayOfbyte, "input");
    m.e(paramArrayOfint, "output");
    paramInt2 = paramInt1 * paramInt2;
    paramInt1 = 0;
    while (paramInt1 < paramInt2) {
      byte b = paramArrayOfbyte[paramInt1];
      try {
        byte b2 = paramArrayOfbyte[paramInt1 / 4 * 2 + paramInt2];
        byte b1 = paramArrayOfbyte[paramInt1 / 4 * 2 + paramInt2 + 1];
        float f2 = (b & 0xFF);
        float f3 = ((b2 & 0xFF) - 128);
        float f1 = ((b1 & 0xFF) - 128);
        int i = e.f((int)(1.402F * f3 + f2), 0, 255);
        int k = e.f((int)(f2 - 0.344F * f1 - f3 * 0.714F), 0, 255);
        paramArrayOfint[paramInt1] = e.f((int)(f2 + f1 * 1.772F), 0, 255) | i << 16 | 0xFF000000 | k << 8;
        paramInt1++;
      } catch (Exception exception) {
        Log.e("PoseLandmarker", "YUV420转RGB失败", exception);
        throw exception;
      } 
    } 
  }
  
  public final void detect(i parami, j.d paramd) {
    m.e(parami, "call");
    m.e(paramd, "result");
    try {
      if (this.poseLandmarker != null) {
        Integer integer = (Integer)parami.a("width");
        if (integer != null) {
          int k = integer.intValue();
          integer = (Integer)parami.a("height");
          if (integer != null) {
            int m = integer.intValue();
            List<Map> list = (List)parami.a("planes");
            if (list != null) {
              int[] arrayOfInt;
              if (list.size() == 3) {
                integer = (Integer)parami.a("sensorOrientation");
                if (integer != null) {
                  int n = integer.intValue();
                  Boolean bool = (Boolean)parami.a("isFront");
                  if (bool != null) {
                    boolean bool1 = bool.booleanValue();
                    int i8 = getDeviceOrientation();
                    Map map2 = list.get(0);
                    Map map1 = list.get(1);
                    Map map3 = list.get(2);
                    bool = (Boolean)map2.get("bytes");
                    m.c(bool, "null cannot be cast to non-null type kotlin.ByteArray");
                    byte[] arrayOfByte1 = (byte[])bool;
                    list = (List<Map>)map1.get("bytes");
                    m.c(list, "null cannot be cast to non-null type kotlin.ByteArray");
                    byte[] arrayOfByte3 = (byte[])list;
                    map3 = (Map)map3.get("bytes");
                    m.c(map3, "null cannot be cast to non-null type kotlin.ByteArray");
                    byte[] arrayOfByte4 = (byte[])map3;
                    map2 = (Map)map2.get("bytesPerRow");
                    m.c(map2, "null cannot be cast to non-null type kotlin.Int");
                    int i3 = ((Integer)map2).intValue();
                    map2 = (Map)map1.get("bytesPerRow");
                    m.c(map2, "null cannot be cast to non-null type kotlin.Int");
                    int i6 = ((Integer)map2).intValue();
                    map1 = (Map)map1.get("bytesPerPixel");
                    m.c(map1, "null cannot be cast to non-null type kotlin.Int");
                    int i7 = ((Integer)map1).intValue();
                    int i5 = k * m;
                    byte[] arrayOfByte2 = new byte[i5 * 3 / 2];
                    int i1 = 0;
                    int i2 = 0;
                    while (i1 < m) {
                      System.arraycopy(arrayOfByte1, i1 * i3, arrayOfByte2, i2, k);
                      i2 += k;
                      i1++;
                    } 
                    i3 = m / 2;
                    int i9 = k / 2;
                    int i4 = i5;
                    byte b = 0;
                    i1 = 0;
                    i2 = 0;
                    while (b < i3) {
                      int i11 = 0;
                      int i10 = i1;
                      i1 = i4;
                      for (i4 = i11; i4 < i9; i4++) {
                        i11 = i1 + 1;
                        arrayOfByte2[i1] = arrayOfByte4[i2];
                        i1 = i11 + 1;
                        arrayOfByte2[i11] = arrayOfByte3[i10];
                        i10 += i7;
                        i2 += i7;
                      } 
                      i4 = i6 - i9 * i7;
                      i10 += i4;
                      i2 += i4;
                      b++;
                      i4 = i1;
                      i1 = i10;
                    } 
                    Bitmap bitmap1 = Bitmap.createBitmap(k, m, Bitmap.Config.ARGB_8888);
                    m.d(bitmap1, "createBitmap(...)");
                    arrayOfInt = new int[i5];
                    convertYUVToRGB(arrayOfByte2, k, m, arrayOfInt);
                    bitmap1.setPixels(arrayOfInt, 0, k, 0, 0, k, m);
                    Bitmap bitmap2 = transformBitmap(bitmap1, n, i8, bool1);
                    BitmapImageBuilder bitmapImageBuilder = new BitmapImageBuilder();
                    this(bitmap2);
                    MPImage mPImage = bitmapImageBuilder.build();
                    PoseLandmarker poseLandmarker = this.poseLandmarker;
                    m.b(poseLandmarker);
                    poseLandmarker.detectAsync(mPImage, System.currentTimeMillis());
                    paramd.a(null);
                  } else {
                    Exception exception = new Exception();
                    this("缺少相机朝向参数");
                    throw exception;
                  } 
                } else {
                  Exception exception = new Exception();
                  this("缺少传感器方向参数");
                  throw exception;
                } 
              } else {
                Exception exception = new Exception();
                StringBuilder stringBuilder = new StringBuilder();
                this();
                stringBuilder.append("图像平面数据格式错误,需要 3 个平面(Y/U/V),实际收到 ");
                stringBuilder.append(arrayOfInt.size());
                stringBuilder.append(" 个平面");
                this(stringBuilder.toString());
                throw exception;
              } 
            } else {
              Exception exception = new Exception();
              this("缺少图像平面数据");
              throw exception;
            } 
          } else {
            Exception exception = new Exception();
            this("缺少图像高度参数");
            throw exception;
          } 
        } else {
          Exception exception = new Exception();
          this("缺少图像宽度参数");
          throw exception;
        } 
      } else {
        Exception exception = new Exception();
        this("请先调用 initialize 方法初始化姿势检测器");
        throw exception;
      } 
    } catch (Exception exception) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("图像检测失败: ");
      stringBuilder.append(exception.getMessage());
      String str = stringBuilder.toString();
      Log.e("PoseLandmarker", str, exception);
      paramd.b("DETECT_ERROR", str, null);
    } 
  }
  
  public final void detectRGBA(i parami, j.d paramd) {
    m.e(parami, "call");
    m.e(paramd, "result");
    try {
      if (this.poseLandmarker != null) {
        Integer integer = (Integer)parami.a("width");
        if (integer != null) {
          int k = integer.intValue();
          integer = (Integer)parami.a("height");
          if (integer != null) {
            int m = integer.intValue();
            byte[] arrayOfByte = (byte[])parami.a("imageData");
            if (arrayOfByte != null) {
              String str = (String)parami.a("imageFormat");
              int n = k * m;
              int i1 = n * 4;
              if (arrayOfByte.length == i1) {
                Integer integer2 = (Integer)parami.a("sensorOrientation");
                Integer integer1 = integer2;
                if (integer2 == null)
                  integer1 = Integer.valueOf(0); 
                int i2 = integer1.intValue();
                Boolean bool2 = (Boolean)parami.a("isFront");
                Boolean bool1 = bool2;
                if (bool2 == null)
                  bool1 = Boolean.FALSE; 
                boolean bool = bool1.booleanValue();
                i1 = getDeviceOrientation();
                Bitmap bitmap = Bitmap.createBitmap(k, m, Bitmap.Config.ARGB_8888);
                m.d(bitmap, "createBitmap(...)");
                int[] arrayOfInt = new int[n];
                convertRGBAToARGB(arrayOfByte, arrayOfInt, n);
                bitmap.setPixels(arrayOfInt, 0, k, 0, 0, k, m);
                bitmap = transformBitmap(bitmap, i2, i1, bool);
                BitmapImageBuilder bitmapImageBuilder = new BitmapImageBuilder();
                this(bitmap);
                MPImage mPImage = bitmapImageBuilder.build();
                PoseLandmarker poseLandmarker = this.poseLandmarker;
                m.b(poseLandmarker);
                poseLandmarker.detectAsync(mPImage, System.currentTimeMillis());
                paramd.a(null);
              } else {
                Exception exception = new Exception();
                StringBuilder stringBuilder = new StringBuilder();
                this();
                stringBuilder.append("RGBA数据大小不符合预期: 实际 ");
                stringBuilder.append(arrayOfByte.length);
                stringBuilder.append(" 字节, 预期 ");
                stringBuilder.append(i1);
                stringBuilder.append(" 字节");
                this(stringBuilder.toString());
                throw exception;
              } 
            } else {
              Exception exception = new Exception();
              this("缺少RGBA图像数据");
              throw exception;
            } 
          } else {
            Exception exception = new Exception();
            this("缺少图像高度参数");
            throw exception;
          } 
        } else {
          Exception exception = new Exception();
          this("缺少图像宽度参数");
          throw exception;
        } 
      } else {
        Exception exception = new Exception();
        this("请先调用 initialize 方法初始化姿势检测器");
        throw exception;
      } 
    } catch (Exception exception) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("RGBA图像检测失败: ");
      stringBuilder.append(exception.getMessage());
      String str = stringBuilder.toString();
      Log.e("PoseLandmarker", str, exception);
      paramd.b("DETECT_RGBA_ERROR", str, null);
    } 
  }
  
  public final long getLastSaveTime() {
    return this.lastSaveTime;
  }
  
  public final PoseLandmarker getPoseLandmarker() {
    return this.poseLandmarker;
  }
  
  public final long getSAVE_INTERVAL() {
    return this.SAVE_INTERVAL;
  }
  
  public final void handleResult(PoseLandmarkerResult paramPoseLandmarkerResult) {
    m.e(paramPoseLandmarkerResult, "result");
    try {
      ArrayList<ArrayList<Map>> arrayList = new ArrayList();
      this();
      for (List list : paramPoseLandmarkerResult.landmarks()) {
        ArrayList<Map> arrayList1 = new ArrayList();
        this();
        for (NormalizedLandmark normalizedLandmark : list) {
          arrayList1.add(b0.f(new g[] { l.a("x", Float.valueOf(normalizedLandmark.x())), l.a("y", Float.valueOf(normalizedLandmark.y())), l.a("z", Float.valueOf(normalizedLandmark.z())), l.a("visibility", normalizedLandmark.visibility().orElse(Float.valueOf(0.0F))), l.a("presence", normalizedLandmark.presence().orElse(Float.valueOf(0.0F))) }));
        } 
        arrayList.add(arrayList1);
      } 
      Handler handler = new Handler();
      this(Looper.getMainLooper());
      vd.a a1 = new vd.a();
      this(this, arrayList);
      handler.post((Runnable)a1);
    } catch (Exception exception) {
      Log.e("PoseLandmarker", "处理检测结果失败", exception);
    } 
  }
  
  public final void initialize(i parami, j.d paramd) {
    m.e(parami, "call");
    m.e(paramd, "result");
    try {
      Log.i("PoseLandmarker", "开始初始化姿势检测器");
      Integer integer2 = (Integer)parami.a("maxPoses");
      Integer integer1 = integer2;
      if (integer2 == null)
        integer1 = Integer.valueOf(1); 
      int k = integer1.intValue();
      PoseLandmarker poseLandmarker = this.poseLandmarker;
      if (poseLandmarker != null)
        poseLandmarker.close(); 
      this.poseLandmarker = null;
      Context context4 = this.context;
      Context context2 = context4;
      if (context4 == null) {
        m.n("context");
        context2 = null;
      } 
      String str = copyAssetToCache(context2, "pose_landmarker_lite.task");
      BaseOptions baseOptions = BaseOptions.builder().setDelegate(Delegate.GPU).setModelAssetPath(str).build();
      PoseLandmarker.PoseLandmarkerOptions.Builder builder = PoseLandmarker.PoseLandmarkerOptions.builder().setBaseOptions(baseOptions).setRunningMode(RunningMode.LIVE_STREAM).setNumPoses(Integer.valueOf(k));
      b b = new b();
      this(this);
      PoseLandmarker.PoseLandmarkerOptions poseLandmarkerOptions = builder.setResultListener((OutputHandler.ResultListener)b).build();
      Context context3 = this.context;
      Context context1 = context3;
      if (context3 == null) {
        m.n("context");
        context1 = null;
      } 
      this.poseLandmarker = PoseLandmarker.createFromOptions(context1, poseLandmarkerOptions);
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("姿势检测器初始化成功,最大检测人数: ");
      stringBuilder.append(k);
      Log.i("PoseLandmarker", stringBuilder.toString());
      paramd.a(null);
    } catch (Exception exception) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("姿势检测器初始化失败: ");
      stringBuilder.append(exception.getMessage());
      String str = stringBuilder.toString();
      Log.e("PoseLandmarker", str, exception);
      paramd.b("INITIALIZE_ERROR", str, null);
    } 
  }
  
  public void onAttachedToActivity(c paramc) {
    m.e(paramc, "binding");
    this.activity = paramc.h();
  }
  
  public void onAttachedToEngine(a.b paramb) {
    m.e(paramb, "binding");
    Log.i("PoseLandmarker", "插件开始初始化");
    j j1 = new j(paramb.b(), "pose_landmarker_plugin");
    this.channel = j1;
    j1.e(this);
    Context context = paramb.a();
    m.d(context, "getApplicationContext(...)");
    this.context = context;
    Log.i("PoseLandmarker", "插件初始化完成");
  }
  
  public void onDetachedFromActivity() {
    this.activity = null;
  }
  
  public void onDetachedFromActivityForConfigChanges() {
    this.activity = null;
  }
  
  public void onDetachedFromEngine(a.b paramb) {
    m.e(paramb, "binding");
    Log.i("PoseLandmarker", "插件开始释放资源");
    j j2 = this.channel;
    j j1 = j2;
    if (j2 == null) {
      m.n("channel");
      j1 = null;
    } 
    j1.e(null);
    PoseLandmarker poseLandmarker = this.poseLandmarker;
    if (poseLandmarker != null)
      poseLandmarker.close(); 
    Log.i("PoseLandmarker", "插件资源释放完成");
  }
  
  public void onMethodCall(i parami, j.d paramd) {
    m.e(parami, "call");
    m.e(paramd, "result");
    String str = parami.a;
    if (str != null) {
      StringBuilder stringBuilder1;
      int k = str.hashCode();
      if (k != -1945733545) {
        if (k != -1335220573) {
          if (k == 871091088 && str.equals("initialize")) {
            initialize(parami, paramd);
            return;
          } 
        } else {
          if (!str.equals("detect")) {
            stringBuilder1 = new StringBuilder();
            stringBuilder1.append("未实现的方法: ");
            stringBuilder1.append(parami.a);
            Log.w("PoseLandmarker", stringBuilder1.toString());
            paramd.c();
            return;
          } 
          detect(parami, paramd);
          return;
        } 
      } else {
        if (!stringBuilder1.equals("detectRGBA")) {
          stringBuilder1 = new StringBuilder();
          stringBuilder1.append("未实现的方法: ");
          stringBuilder1.append(parami.a);
          Log.w("PoseLandmarker", stringBuilder1.toString());
          paramd.c();
          return;
        } 
        detectRGBA(parami, paramd);
        return;
      } 
    } 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("未实现的方法: ");
    stringBuilder.append(parami.a);
    Log.w("PoseLandmarker", stringBuilder.toString());
    paramd.c();
  }
  
  public void onReattachedToActivityForConfigChanges(c paramc) {
    m.e(paramc, "binding");
    this.activity = paramc.h();
  }
  
  public final void saveImageToGallery(Bitmap paramBitmap, int paramInt1, int paramInt2, Context paramContext) {
    m.e(paramBitmap, "bitmap");
    m.e(paramContext, "context");
    long l = System.currentTimeMillis();
    if (l - this.lastSaveTime < this.SAVE_INTERVAL)
      return; 
    try {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
      this("yyyyMMdd_HHmmss", Locale.getDefault());
      Date date = new Date();
      this();
      String str = simpleDateFormat.format(date);
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("POSE_");
      stringBuilder.append(str);
      stringBuilder.append('_');
      stringBuilder.append(paramInt1);
      stringBuilder.append('_');
      stringBuilder.append(paramInt2);
      stringBuilder.append(".jpg");
      str = stringBuilder.toString();
      File file2 = new File();
      this(paramContext.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "poses");
      if (!file2.exists())
        file2.mkdirs(); 
      File file1 = new File();
      this(file2, str);
      FileOutputStream fileOutputStream = new FileOutputStream();
      this(file1);
      try {
        paramBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
        fileOutputStream.flush();
        o o = o.a;
        b.a(fileOutputStream, null);
        StringBuilder stringBuilder1 = new StringBuilder();
        this();
        stringBuilder1.append("图片已保存: ");
        stringBuilder1.append(file1.getAbsolutePath());
        Log.i("PoseLandmarker", stringBuilder1.toString());
      } finally {
        paramBitmap = null;
      } 
    } catch (Exception exception) {
      Log.e("PoseLandmarker", "保存图片失败", exception);
    } 
  }
  
  public final void setLastSaveTime(long paramLong) {
    this.lastSaveTime = paramLong;
  }
  
  public final void setPoseLandmarker(PoseLandmarker paramPoseLandmarker) {
    this.poseLandmarker = paramPoseLandmarker;
  }
  
  /**
   * 转换图像方向和镜像
   * @param bitmap 原始图像
   * @param sensorOrientation 传感器方向 (0/90/180/270)
   * @param deviceOrientation 设备方向 (0/90/180/270) 
   * @param isFront 是否前置摄像头
   * @return 转换后的图像
   */
  public final Bitmap transformBitmap(Bitmap bitmap, int sensorOrientation, int deviceOrientation, boolean isFront) {
    m.e(bitmap, "bitmap");
    
    Matrix matrix = new Matrix();
    
    // 计算旋转角度
    int rotationDegrees = 0;
    switch (deviceOrientation) {
      case 90:
        rotationDegrees = 90;
        break;
      case 180:
        rotationDegrees = 180;
        break;  
      case 270:
        rotationDegrees = 270;
        break;
    }
    
    // 根据前后摄像头调整旋转方向
    int rotation;
    if (isFront) {
      rotation = (360 - (sensorOrientation + rotationDegrees) % 360) % 360;
    } else {
      rotation = ((sensorOrientation - rotationDegrees) + 360) % 360;
    }
    
    // 应用旋转
    matrix.postRotate(rotation);
    
    // 前置摄像头需要镜像
    if (isFront) {
      if (deviceOrientation == 90 || deviceOrientation == 270) {
        matrix.postScale(-1.0f, 1.0f);
      } else if (deviceOrientation == 0 || deviceOrientation == 180) {
        matrix.postScale(1.0f, -1.0f);
      }
    }
    
    try {
      // 创建转换后的图像
      Bitmap transformedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
      m.d(transformedBitmap, "createBitmap(...)");
      return transformedBitmap;
    } catch (Exception e) {
      Log.e("PoseLandmarker", "旋转或翻转失败", e);
      throw e;
    }
  }
  
  public static final class a {
    public a() {}
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\com\jxhy\partr\PoseLandmarkerPlugin.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */