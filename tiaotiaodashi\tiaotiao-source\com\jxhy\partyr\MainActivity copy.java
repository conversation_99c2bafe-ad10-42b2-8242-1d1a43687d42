package com.jxhy.partyr;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import com.jxhy.partr.PoseLandmarkerPlugin;
import gh.o;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import kg.g;
import kg.l;
import lf.b;
import lf.i;
import lf.j;
import lg.a0;
import lg.b0;
import lg.l;
import wd.b;
import wd.c;
import xe.d;
import xg.g;
import xg.m;

public final class MainActivity extends d {
  public static final a h = new a(null);
  
  public j f;
  
  public j g;
  
  public static final void c0(i parami, j.d paramd) {
    m.e(parami, "call");
    m.e(paramd, "result");
    if (m.a(parami.a, "getInitialImportFile")) {
      paramd.a(s7.a.a.d());
    } else {
      paramd.c();
    } 
  }
  
  public static final void d0(MainActivity paramMainActivity, i parami, j.d paramd) {
    m.e(paramMainActivity, "this$0");
    m.e(parami, "call");
    m.e(paramd, "result");
    String str = parami.a;
    if (m.a(str, "stopAllForegroundServices")) {
      try {
        paramd.a(paramMainActivity.j0());
      } catch (Exception exception) {
        Log.e("MainActivity", "停止前台服务时发生错误", exception);
        str = "STOP_SERVICES_ERROR";
        paramd.b(str, exception.getMessage(), null);
      } 
    } else if (m.a(str, "checkRunningServices")) {
      try {
        paramd.a(exception.a0());
      } catch (Exception exception1) {
        Log.e("MainActivity", "检查运行服务时发生错误", exception1);
        str = "CHECK_SERVICES_ERROR";
        paramd.b(str, exception1.getMessage(), null);
      } 
    } else {
      paramd.c();
    } 
  }
  
  public static final void e0(MainActivity paramMainActivity, i parami, j.d paramd) {
    m.e(paramMainActivity, "this$0");
    m.e(parami, "call");
    m.e(paramd, "result");
    if (m.a(parami.a, "saveToGallery")) {
      try {
        String str = (String)parami.a("path");
        if (str != null) {
          paramd.a(paramMainActivity.h0(str));
        } else {
          paramd.b("INVALID_PATH", "Path is null", null);
        } 
      } catch (Exception exception) {
        paramd.b("SAVE_ERROR", exception.getMessage(), null);
      } 
    } else {
      paramd.c();
    } 
  }
  
  public final Map<String, Object> a0() {
    LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<Object, Object>();
    ArrayList<Map> arrayList2 = new ArrayList();
    ArrayList<Map> arrayList1 = new ArrayList();
    ArrayList<Map> arrayList3 = new ArrayList();
    try {
      null = getSystemService("activity");
      m.c(null, "null cannot be cast to non-null type android.app.ActivityManager");
      for (ActivityManager.RunningServiceInfo runningServiceInfo : ((ActivityManager)null).getRunningServices(2147483647)) {
        if (m.a(runningServiceInfo.service.getPackageName(), getPackageName())) {
          g g2 = l.a("className", runningServiceInfo.service.getClassName());
          g g1 = l.a("isForeground", Boolean.valueOf(runningServiceInfo.foreground));
          String str2 = runningServiceInfo.service.getClassName();
          m.d(str2, "getClassName(...)");
          Map map = b0.f(new g[] { g2, g1, l.a("isRecordingRelated", Boolean.valueOf(g0(str2))), l.a("pid", Integer.valueOf(runningServiceInfo.pid)), l.a("uid", Integer.valueOf(runningServiceInfo.uid)) });
          arrayList2.add(map);
          if (runningServiceInfo.foreground)
            arrayList1.add(map); 
          String str1 = runningServiceInfo.service.getClassName();
          m.d(str1, "getClassName(...)");
          if (g0(str1))
            arrayList3.add(map); 
        } 
      } 
      linkedHashMap.put("success", Boolean.TRUE);
      linkedHashMap.put("totalServices", Integer.valueOf(arrayList2.size()));
      linkedHashMap.put("foregroundServicesCount", Integer.valueOf(arrayList1.size()));
      linkedHashMap.put("recordingServicesCount", Integer.valueOf(arrayList3.size()));
      linkedHashMap.put("allServices", arrayList2);
      linkedHashMap.put("foregroundServices", arrayList1);
      linkedHashMap.put("recordingServices", arrayList3);
      StringBuilder stringBuilder = new StringBuilder();
      this();
      stringBuilder.append("服务检查完成: 总计");
      stringBuilder.append(arrayList2.size());
      stringBuilder.append("个, 前台");
      stringBuilder.append(arrayList1.size());
      stringBuilder.append("个, 录屏相关");
      stringBuilder.append(arrayList3.size());
      stringBuilder.append('个');
      Log.d("MainActivity", stringBuilder.toString());
    } catch (Exception exception) {
      Log.e("MainActivity", "检查运行服务时发生错误", exception);
      linkedHashMap.put("success", Boolean.FALSE);
      String str2 = exception.getMessage();
      String str1 = str2;
      if (str2 == null)
        str1 = "未知错误"; 
      linkedHashMap.put("error", str1);
    } 
    return (Map)linkedHashMap;
  }
  
  public final void b0(String paramString1, String paramString2) {
    // Byte code:
    //   0: new android/media/MediaExtractor
    //   3: dup
    //   4: invokespecial <init> : ()V
    //   7: astore #7
    //   9: new android/media/MediaMuxer
    //   12: dup
    //   13: aload_2
    //   14: iconst_0
    //   15: invokespecial <init> : (Ljava/lang/String;I)V
    //   18: astore #8
    //   20: new java/lang/StringBuilder
    //   23: astore #9
    //   25: aload #9
    //   27: invokespecial <init> : ()V
    //   30: aload #9
    //   32: ldc_w '开始压缩视频: 输入路径='
    //   35: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   38: pop
    //   39: aload #9
    //   41: aload_1
    //   42: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   45: pop
    //   46: aload #9
    //   48: invokevirtual toString : ()Ljava/lang/String;
    //   51: astore #9
    //   53: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   56: aload #9
    //   58: invokevirtual println : (Ljava/lang/Object;)V
    //   61: aload #7
    //   63: aload_1
    //   64: invokevirtual setDataSource : (Ljava/lang/String;)V
    //   67: aload #7
    //   69: invokevirtual getTrackCount : ()I
    //   72: istore #5
    //   74: iconst_0
    //   75: istore_3
    //   76: iload_3
    //   77: iload #5
    //   79: if_icmpge -> 284
    //   82: aload #7
    //   84: iload_3
    //   85: invokevirtual getTrackFormat : (I)Landroid/media/MediaFormat;
    //   88: astore_1
    //   89: aload_1
    //   90: ldc_w 'getTrackFormat(...)'
    //   93: invokestatic d : (Ljava/lang/Object;Ljava/lang/String;)V
    //   96: aload_1
    //   97: ldc_w 'mime'
    //   100: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
    //   103: astore #9
    //   105: new java/lang/StringBuilder
    //   108: astore #10
    //   110: aload #10
    //   112: invokespecial <init> : ()V
    //   115: aload #10
    //   117: ldc_w '检查轨道 '
    //   120: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   123: pop
    //   124: aload #10
    //   126: iload_3
    //   127: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   130: pop
    //   131: aload #10
    //   133: ldc_w ': MIME类型='
    //   136: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   139: pop
    //   140: aload #10
    //   142: aload #9
    //   144: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   147: pop
    //   148: aload #10
    //   150: invokevirtual toString : ()Ljava/lang/String;
    //   153: astore #10
    //   155: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   158: aload #10
    //   160: invokevirtual println : (Ljava/lang/Object;)V
    //   163: aload #9
    //   165: ifnull -> 193
    //   168: aload #9
    //   170: ldc_w 'video/'
    //   173: iconst_0
    //   174: iconst_2
    //   175: aconst_null
    //   176: invokestatic B : (Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
    //   179: istore #6
    //   181: iconst_1
    //   182: istore #4
    //   184: iload #6
    //   186: iconst_1
    //   187: if_icmpne -> 193
    //   190: goto -> 196
    //   193: iconst_0
    //   194: istore #4
    //   196: iload #4
    //   198: ifeq -> 278
    //   201: new java/lang/StringBuilder
    //   204: astore #9
    //   206: aload #9
    //   208: invokespecial <init> : ()V
    //   211: aload #9
    //   213: ldc_w '找到视频轨道: index='
    //   216: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   219: pop
    //   220: aload #9
    //   222: iload_3
    //   223: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   226: pop
    //   227: aload #9
    //   229: invokevirtual toString : ()Ljava/lang/String;
    //   232: astore #9
    //   234: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   237: aload #9
    //   239: invokevirtual println : (Ljava/lang/Object;)V
    //   242: aload_1
    //   243: ldc_w 'bitrate'
    //   246: ldc_w 500000
    //   249: invokevirtual setInteger : (Ljava/lang/String;I)V
    //   252: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   255: ldc_w '设置比特率: 500Kbps'
    //   258: invokevirtual println : (Ljava/lang/Object;)V
    //   261: aload #8
    //   263: aload_1
    //   264: invokevirtual addTrack : (Landroid/media/MediaFormat;)I
    //   267: istore #5
    //   269: iload_3
    //   270: istore #4
    //   272: iload #5
    //   274: istore_3
    //   275: goto -> 289
    //   278: iinc #3, 1
    //   281: goto -> 76
    //   284: iconst_m1
    //   285: istore_3
    //   286: iconst_m1
    //   287: istore #4
    //   289: iload #4
    //   291: iconst_m1
    //   292: if_icmpeq -> 442
    //   295: ldc_w 1048576
    //   298: invokestatic allocate : (I)Ljava/nio/ByteBuffer;
    //   301: astore_1
    //   302: new android/media/MediaCodec$BufferInfo
    //   305: astore #9
    //   307: aload #9
    //   309: invokespecial <init> : ()V
    //   312: aload #8
    //   314: invokevirtual start : ()V
    //   317: aload #7
    //   319: iload #4
    //   321: invokevirtual selectTrack : (I)V
    //   324: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   327: ldc_w '开始写入压缩后的视频数据'
    //   330: invokevirtual println : (Ljava/lang/Object;)V
    //   333: aload #7
    //   335: aload_1
    //   336: iconst_0
    //   337: invokevirtual readSampleData : (Ljava/nio/ByteBuffer;I)I
    //   340: istore #4
    //   342: iload #4
    //   344: iflt -> 392
    //   347: aload #9
    //   349: aload #7
    //   351: invokevirtual getSampleTime : ()J
    //   354: putfield presentationTimeUs : J
    //   357: aload #9
    //   359: aload #7
    //   361: invokevirtual getSampleFlags : ()I
    //   364: putfield flags : I
    //   367: aload #9
    //   369: iload #4
    //   371: putfield size : I
    //   374: aload #8
    //   376: iload_3
    //   377: aload_1
    //   378: aload #9
    //   380: invokevirtual writeSampleData : (ILjava/nio/ByteBuffer;Landroid/media/MediaCodec$BufferInfo;)V
    //   383: aload #7
    //   385: invokevirtual advance : ()Z
    //   388: pop
    //   389: goto -> 333
    //   392: new java/lang/StringBuilder
    //   395: astore_1
    //   396: aload_1
    //   397: invokespecial <init> : ()V
    //   400: aload_1
    //   401: ldc_w '视频压缩完成: 输出路径='
    //   404: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   407: pop
    //   408: aload_1
    //   409: aload_2
    //   410: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   413: pop
    //   414: aload_1
    //   415: invokevirtual toString : ()Ljava/lang/String;
    //   418: astore_1
    //   419: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   422: aload_1
    //   423: invokevirtual println : (Ljava/lang/Object;)V
    //   426: aload #8
    //   428: invokevirtual stop : ()V
    //   431: aload #8
    //   433: invokevirtual release : ()V
    //   436: aload #7
    //   438: invokevirtual release : ()V
    //   441: return
    //   442: new java/io/IOException
    //   445: astore_1
    //   446: aload_1
    //   447: ldc_w 'No video track found'
    //   450: invokespecial <init> : (Ljava/lang/String;)V
    //   453: aload_1
    //   454: athrow
    //   455: astore_1
    //   456: goto -> 499
    //   459: astore_1
    //   460: new java/lang/StringBuilder
    //   463: astore_2
    //   464: aload_2
    //   465: invokespecial <init> : ()V
    //   468: aload_2
    //   469: ldc_w '视频压缩失败: '
    //   472: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   475: pop
    //   476: aload_2
    //   477: aload_1
    //   478: invokevirtual getMessage : ()Ljava/lang/String;
    //   481: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   484: pop
    //   485: aload_2
    //   486: invokevirtual toString : ()Ljava/lang/String;
    //   489: astore_2
    //   490: getstatic java/lang/System.out : Ljava/io/PrintStream;
    //   493: aload_2
    //   494: invokevirtual println : (Ljava/lang/Object;)V
    //   497: aload_1
    //   498: athrow
    //   499: aload #8
    //   501: invokevirtual stop : ()V
    //   504: aload #8
    //   506: invokevirtual release : ()V
    //   509: aload #7
    //   511: invokevirtual release : ()V
    //   514: aload_1
    //   515: athrow
    // Exception table:
    //   from	to	target	type
    //   20	74	459	java/lang/Exception
    //   20	74	455	finally
    //   82	163	459	java/lang/Exception
    //   82	163	455	finally
    //   168	181	459	java/lang/Exception
    //   168	181	455	finally
    //   201	269	459	java/lang/Exception
    //   201	269	455	finally
    //   295	333	459	java/lang/Exception
    //   295	333	455	finally
    //   333	342	459	java/lang/Exception
    //   333	342	455	finally
    //   347	389	459	java/lang/Exception
    //   347	389	455	finally
    //   392	426	459	java/lang/Exception
    //   392	426	455	finally
    //   442	455	459	java/lang/Exception
    //   442	455	455	finally
    //   460	499	455	finally
  }
  
  public final void f0(Intent paramIntent) {
    if (paramIntent == null)
      return; 
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("处理Intent: action=");
    stringBuilder.append(paramIntent.getAction());
    stringBuilder.append(", data=");
    stringBuilder.append(paramIntent.getData());
    Log.d("MainActivity", stringBuilder.toString());
    s7.a.a a1 = s7.a.a;
    if (a1.f(paramIntent, (Context)this)) {
      Log.i("MainActivity", "文件导入Intent处理成功");
      String str = a1.d();
      if (str != null) {
        j j1 = this.f;
        if (j1 != null && j1 != null)
          j1.c("importFile", a0.b(l.a("filePath", str))); 
      } 
    } else {
      Log.w("MainActivity", "文件导入Intent处理失败");
    } 
  }
  
  public final boolean g0(String paramString) {
    List list = l.k((Object[])new String[] { "ScreenRecording", "ForegroundService", "mediaProjection", "recording", "screen_recording" });
    boolean bool = list instanceof java.util.Collection;
    boolean bool1 = false;
    if (bool && list.isEmpty()) {
      bool = bool1;
    } else {
      Iterator<String> iterator = list.iterator();
      while (true) {
        bool = bool1;
        if (iterator.hasNext()) {
          if (o.E(paramString, iterator.next(), true)) {
            bool = true;
            break;
          } 
          continue;
        } 
        break;
      } 
    } 
    return bool;
  }
  
  public final Map<String, String> h0(String paramString) {
    // Byte code:
    //   0: new java/lang/StringBuilder
    //   3: astore_2
    //   4: aload_2
    //   5: invokespecial <init> : ()V
    //   8: aload_2
    //   9: ldc_w 'Screen_Recording_'
    //   12: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   15: pop
    //   16: aload_2
    //   17: invokestatic currentTimeMillis : ()J
    //   20: invokevirtual append : (J)Ljava/lang/StringBuilder;
    //   23: pop
    //   24: aload_2
    //   25: invokevirtual toString : ()Ljava/lang/String;
    //   28: astore #5
    //   30: new java/io/File
    //   33: astore #4
    //   35: aload_0
    //   36: invokevirtual a : ()Landroid/content/Context;
    //   39: invokevirtual getCacheDir : ()Ljava/io/File;
    //   42: astore_2
    //   43: new java/lang/StringBuilder
    //   46: astore_3
    //   47: aload_3
    //   48: invokespecial <init> : ()V
    //   51: aload_3
    //   52: aload #5
    //   54: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   57: pop
    //   58: aload_3
    //   59: ldc_w '_compressed.mp4'
    //   62: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   65: pop
    //   66: aload #4
    //   68: aload_2
    //   69: aload_3
    //   70: invokevirtual toString : ()Ljava/lang/String;
    //   73: invokespecial <init> : (Ljava/io/File;Ljava/lang/String;)V
    //   76: aload #4
    //   78: invokevirtual getAbsolutePath : ()Ljava/lang/String;
    //   81: astore_2
    //   82: aload_2
    //   83: ldc_w 'getAbsolutePath(...)'
    //   86: invokestatic d : (Ljava/lang/Object;Ljava/lang/String;)V
    //   89: aload_0
    //   90: aload_1
    //   91: aload_2
    //   92: invokevirtual b0 : (Ljava/lang/String;Ljava/lang/String;)V
    //   95: new android/content/ContentValues
    //   98: astore_1
    //   99: aload_1
    //   100: invokespecial <init> : ()V
    //   103: aload_1
    //   104: ldc_w '_display_name'
    //   107: aload #5
    //   109: invokevirtual put : (Ljava/lang/String;Ljava/lang/String;)V
    //   112: aload_1
    //   113: ldc_w 'mime_type'
    //   116: ldc_w 'video/mp4'
    //   119: invokevirtual put : (Ljava/lang/String;Ljava/lang/String;)V
    //   122: getstatic android/os/Build$VERSION.SDK_INT : I
    //   125: bipush #29
    //   127: if_icmplt -> 140
    //   130: aload_1
    //   131: ldc_w 'relative_path'
    //   134: ldc_w 'Movies/ScreenRecordings'
    //   137: invokevirtual put : (Ljava/lang/String;Ljava/lang/String;)V
    //   140: aload_0
    //   141: invokevirtual a : ()Landroid/content/Context;
    //   144: invokevirtual getContentResolver : ()Landroid/content/ContentResolver;
    //   147: astore #6
    //   149: aload #6
    //   151: getstatic android/provider/MediaStore$Video$Media.EXTERNAL_CONTENT_URI : Landroid/net/Uri;
    //   154: aload_1
    //   155: invokevirtual insert : (Landroid/net/Uri;Landroid/content/ContentValues;)Landroid/net/Uri;
    //   158: astore #7
    //   160: aconst_null
    //   161: astore_1
    //   162: aload #7
    //   164: ifnull -> 221
    //   167: aload #6
    //   169: aload #7
    //   171: invokevirtual openOutputStream : (Landroid/net/Uri;)Ljava/io/OutputStream;
    //   174: astore_2
    //   175: aload_2
    //   176: ifnull -> 221
    //   179: new java/io/FileInputStream
    //   182: astore_3
    //   183: aload_3
    //   184: aload #4
    //   186: invokespecial <init> : (Ljava/io/File;)V
    //   189: aload_2
    //   190: invokestatic b : (Ljava/lang/Object;)V
    //   193: aload_3
    //   194: aload_2
    //   195: iconst_0
    //   196: iconst_2
    //   197: aconst_null
    //   198: invokestatic b : (Ljava/io/InputStream;Ljava/io/OutputStream;IILjava/lang/Object;)J
    //   201: pop2
    //   202: aload_2
    //   203: aconst_null
    //   204: invokestatic a : (Ljava/io/Closeable;Ljava/lang/Throwable;)V
    //   207: goto -> 221
    //   210: astore_1
    //   211: aload_1
    //   212: athrow
    //   213: astore_3
    //   214: aload_2
    //   215: aload_1
    //   216: invokestatic a : (Ljava/io/Closeable;Ljava/lang/Throwable;)V
    //   219: aload_3
    //   220: athrow
    //   221: ldc_w ''
    //   224: astore_3
    //   225: aload #7
    //   227: ifnull -> 246
    //   230: aload #7
    //   232: invokevirtual getLastPathSegment : ()Ljava/lang/String;
    //   235: astore_2
    //   236: aload_2
    //   237: ifnonnull -> 243
    //   240: goto -> 246
    //   243: goto -> 250
    //   246: ldc_w ''
    //   249: astore_2
    //   250: aload #7
    //   252: invokestatic b : (Ljava/lang/Object;)V
    //   255: aload #6
    //   257: aload #7
    //   259: iconst_1
    //   260: anewarray java/lang/String
    //   263: dup
    //   264: iconst_0
    //   265: ldc_w '_data'
    //   268: aastore
    //   269: aconst_null
    //   270: aconst_null
    //   271: aconst_null
    //   272: invokevirtual query : (Landroid/net/Uri;[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;)Landroid/database/Cursor;
    //   275: astore #6
    //   277: aload #6
    //   279: ifnull -> 338
    //   282: aload #6
    //   284: invokeinterface moveToFirst : ()Z
    //   289: ifeq -> 313
    //   292: aload #6
    //   294: aload #6
    //   296: ldc_w '_data'
    //   299: invokeinterface getColumnIndexOrThrow : (Ljava/lang/String;)I
    //   304: invokeinterface getString : (I)Ljava/lang/String;
    //   309: astore_1
    //   310: goto -> 317
    //   313: ldc_w ''
    //   316: astore_1
    //   317: aload #6
    //   319: aconst_null
    //   320: invokestatic a : (Ljava/io/Closeable;Ljava/lang/Throwable;)V
    //   323: goto -> 338
    //   326: astore_2
    //   327: aload_2
    //   328: athrow
    //   329: astore_1
    //   330: aload #6
    //   332: aload_2
    //   333: invokestatic a : (Ljava/io/Closeable;Ljava/lang/Throwable;)V
    //   336: aload_1
    //   337: athrow
    //   338: aload_1
    //   339: ifnonnull -> 347
    //   342: aload_3
    //   343: astore_1
    //   344: goto -> 347
    //   347: aload #4
    //   349: invokevirtual delete : ()Z
    //   352: pop
    //   353: iconst_3
    //   354: anewarray kg/g
    //   357: dup
    //   358: iconst_0
    //   359: ldc_w 'fileName'
    //   362: aload #5
    //   364: invokestatic a : (Ljava/lang/Object;Ljava/lang/Object;)Lkg/g;
    //   367: aastore
    //   368: dup
    //   369: iconst_1
    //   370: ldc_w 'id'
    //   373: aload_2
    //   374: invokestatic a : (Ljava/lang/Object;Ljava/lang/Object;)Lkg/g;
    //   377: aastore
    //   378: dup
    //   379: iconst_2
    //   380: ldc 'path'
    //   382: aload_1
    //   383: invokestatic a : (Ljava/lang/Object;Ljava/lang/Object;)Lkg/g;
    //   386: aastore
    //   387: invokestatic f : ([Lkg/g;)Ljava/util/Map;
    //   390: astore_1
    //   391: aload_1
    //   392: areturn
    //   393: astore_1
    //   394: aload_1
    //   395: invokevirtual printStackTrace : ()V
    //   398: aload_1
    //   399: athrow
    // Exception table:
    //   from	to	target	type
    //   0	140	393	java/io/IOException
    //   140	160	393	java/io/IOException
    //   167	175	393	java/io/IOException
    //   179	202	210	finally
    //   202	207	393	java/io/IOException
    //   211	213	213	finally
    //   214	221	393	java/io/IOException
    //   230	236	393	java/io/IOException
    //   250	277	393	java/io/IOException
    //   282	310	326	finally
    //   317	323	393	java/io/IOException
    //   327	329	329	finally
    //   330	338	393	java/io/IOException
    //   347	391	393	java/io/IOException
  }
  
  public final void i0(Window paramWindow) {
    if (paramWindow == null)
      return; 
    WindowManager.LayoutParams layoutParams = paramWindow.getAttributes();
    try {
      Class<?> clazz = Class.forName("com.huawei.android.view.LayoutParamsEx");
      layoutParams = clazz.getConstructor(new Class[] { WindowManager.LayoutParams.class }).newInstance(new Object[] { layoutParams });
      Method method = clazz.getMethod("addHwFlags", new Class[] { int.class });
      m.d(method, "getMethod(...)");
      method.invoke(layoutParams, new Object[] { Integer.valueOf(65536) });
    } catch (ClassNotFoundException|NoSuchMethodException|IllegalAccessException|InstantiationException|java.lang.reflect.InvocationTargetException|Exception classNotFoundException) {}
  }
  
  public final Map<String, Object> j0() {
    // Byte code:
    //   0: new java/util/LinkedHashMap
    //   3: dup
    //   4: invokespecial <init> : ()V
    //   7: astore #7
    //   9: new java/util/ArrayList
    //   12: dup
    //   13: invokespecial <init> : ()V
    //   16: astore #8
    //   18: iconst_0
    //   19: istore_3
    //   20: iconst_0
    //   21: istore_2
    //   22: iload_3
    //   23: istore_1
    //   24: ldc 'MainActivity'
    //   26: ldc_w '🚨 开始清理前台服务以防止VIVO自启动检测'
    //   29: invokestatic i : (Ljava/lang/String;Ljava/lang/String;)I
    //   32: pop
    //   33: iload_3
    //   34: istore_1
    //   35: aload_0
    //   36: ldc 'activity'
    //   38: invokevirtual getSystemService : (Ljava/lang/String;)Ljava/lang/Object;
    //   41: astore #5
    //   43: iload_3
    //   44: istore_1
    //   45: aload #5
    //   47: ldc 'null cannot be cast to non-null type android.app.ActivityManager'
    //   49: invokestatic c : (Ljava/lang/Object;Ljava/lang/String;)V
    //   52: iload_3
    //   53: istore_1
    //   54: aload #5
    //   56: checkcast android/app/ActivityManager
    //   59: ldc 2147483647
    //   61: invokevirtual getRunningServices : (I)Ljava/util/List;
    //   64: astore #6
    //   66: iload_3
    //   67: istore_1
    //   68: new java/lang/StringBuilder
    //   71: astore #5
    //   73: iload_3
    //   74: istore_1
    //   75: aload #5
    //   77: invokespecial <init> : ()V
    //   80: iload_3
    //   81: istore_1
    //   82: aload #5
    //   84: ldc_w '检测到 '
    //   87: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   90: pop
    //   91: iload_3
    //   92: istore_1
    //   93: aload #5
    //   95: aload #6
    //   97: invokeinterface size : ()I
    //   102: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   105: pop
    //   106: iload_3
    //   107: istore_1
    //   108: aload #5
    //   110: ldc_w ' 个运行中的服务'
    //   113: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   116: pop
    //   117: iload_3
    //   118: istore_1
    //   119: ldc 'MainActivity'
    //   121: aload #5
    //   123: invokevirtual toString : ()Ljava/lang/String;
    //   126: invokestatic d : (Ljava/lang/String;Ljava/lang/String;)I
    //   129: pop
    //   130: iload_3
    //   131: istore_1
    //   132: aload #6
    //   134: invokeinterface iterator : ()Ljava/util/Iterator;
    //   139: astore #6
    //   141: iload_2
    //   142: istore_1
    //   143: aload #6
    //   145: invokeinterface hasNext : ()Z
    //   150: ifeq -> 517
    //   153: iload_2
    //   154: istore_1
    //   155: aload #6
    //   157: invokeinterface next : ()Ljava/lang/Object;
    //   162: checkcast android/app/ActivityManager$RunningServiceInfo
    //   165: astore #9
    //   167: iload_2
    //   168: istore_1
    //   169: aload #9
    //   171: getfield service : Landroid/content/ComponentName;
    //   174: invokevirtual getPackageName : ()Ljava/lang/String;
    //   177: aload_0
    //   178: invokevirtual getPackageName : ()Ljava/lang/String;
    //   181: invokestatic a : (Ljava/lang/Object;Ljava/lang/Object;)Z
    //   184: ifeq -> 141
    //   187: iload_2
    //   188: istore_1
    //   189: aload #9
    //   191: getfield service : Landroid/content/ComponentName;
    //   194: invokevirtual getClassName : ()Ljava/lang/String;
    //   197: astore #5
    //   199: iload_2
    //   200: istore_1
    //   201: aload #5
    //   203: ldc 'getClassName(...)'
    //   205: invokestatic d : (Ljava/lang/Object;Ljava/lang/String;)V
    //   208: iload_2
    //   209: istore_1
    //   210: new java/lang/StringBuilder
    //   213: astore #10
    //   215: iload_2
    //   216: istore_1
    //   217: aload #10
    //   219: invokespecial <init> : ()V
    //   222: iload_2
    //   223: istore_1
    //   224: aload #10
    //   226: ldc_w '检查服务: '
    //   229: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   232: pop
    //   233: iload_2
    //   234: istore_1
    //   235: aload #10
    //   237: aload #5
    //   239: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   242: pop
    //   243: iload_2
    //   244: istore_1
    //   245: aload #10
    //   247: ldc_w ', 前台服务: '
    //   250: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   253: pop
    //   254: iload_2
    //   255: istore_1
    //   256: aload #10
    //   258: aload #9
    //   260: getfield foreground : Z
    //   263: invokevirtual append : (Z)Ljava/lang/StringBuilder;
    //   266: pop
    //   267: iload_2
    //   268: istore_1
    //   269: ldc 'MainActivity'
    //   271: aload #10
    //   273: invokevirtual toString : ()Ljava/lang/String;
    //   276: invokestatic d : (Ljava/lang/String;Ljava/lang/String;)I
    //   279: pop
    //   280: iload_2
    //   281: istore_1
    //   282: aload #9
    //   284: getfield foreground : Z
    //   287: ifeq -> 141
    //   290: iload_2
    //   291: istore_1
    //   292: aload_0
    //   293: aload #5
    //   295: invokevirtual g0 : (Ljava/lang/String;)Z
    //   298: istore #4
    //   300: iload #4
    //   302: ifeq -> 141
    //   305: iload_2
    //   306: istore_3
    //   307: new android/content/Intent
    //   310: astore #10
    //   312: iload_2
    //   313: istore_3
    //   314: aload #10
    //   316: invokespecial <init> : ()V
    //   319: iload_2
    //   320: istore_3
    //   321: aload #10
    //   323: aload #9
    //   325: getfield service : Landroid/content/ComponentName;
    //   328: invokevirtual setComponent : (Landroid/content/ComponentName;)Landroid/content/Intent;
    //   331: pop
    //   332: iload_2
    //   333: istore_3
    //   334: aload_0
    //   335: aload #10
    //   337: invokevirtual stopService : (Landroid/content/Intent;)Z
    //   340: ifeq -> 409
    //   343: iinc #2, 1
    //   346: iload_2
    //   347: istore_3
    //   348: aload #8
    //   350: aload #5
    //   352: invokeinterface add : (Ljava/lang/Object;)Z
    //   357: pop
    //   358: iload_2
    //   359: istore_3
    //   360: new java/lang/StringBuilder
    //   363: astore #9
    //   365: iload_2
    //   366: istore_3
    //   367: aload #9
    //   369: invokespecial <init> : ()V
    //   372: iload_2
    //   373: istore_3
    //   374: aload #9
    //   376: ldc_w '✅ 成功停止前台服务: '
    //   379: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   382: pop
    //   383: iload_2
    //   384: istore_3
    //   385: aload #9
    //   387: aload #5
    //   389: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   392: pop
    //   393: iload_2
    //   394: istore_3
    //   395: ldc 'MainActivity'
    //   397: aload #9
    //   399: invokevirtual toString : ()Ljava/lang/String;
    //   402: invokestatic i : (Ljava/lang/String;Ljava/lang/String;)I
    //   405: pop
    //   406: goto -> 141
    //   409: iload_2
    //   410: istore_3
    //   411: new java/lang/StringBuilder
    //   414: astore #9
    //   416: iload_2
    //   417: istore_3
    //   418: aload #9
    //   420: invokespecial <init> : ()V
    //   423: iload_2
    //   424: istore_3
    //   425: aload #9
    //   427: ldc_w '⚠️ 停止前台服务失败: '
    //   430: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   433: pop
    //   434: iload_2
    //   435: istore_3
    //   436: aload #9
    //   438: aload #5
    //   440: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   443: pop
    //   444: iload_2
    //   445: istore_3
    //   446: ldc 'MainActivity'
    //   448: aload #9
    //   450: invokevirtual toString : ()Ljava/lang/String;
    //   453: invokestatic w : (Ljava/lang/String;Ljava/lang/String;)I
    //   456: pop
    //   457: goto -> 141
    //   460: astore #9
    //   462: iload_3
    //   463: istore_1
    //   464: new java/lang/StringBuilder
    //   467: astore #10
    //   469: iload_3
    //   470: istore_1
    //   471: aload #10
    //   473: invokespecial <init> : ()V
    //   476: iload_3
    //   477: istore_1
    //   478: aload #10
    //   480: ldc_w '停止服务时发生异常: '
    //   483: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   486: pop
    //   487: iload_3
    //   488: istore_1
    //   489: aload #10
    //   491: aload #5
    //   493: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   496: pop
    //   497: iload_3
    //   498: istore_1
    //   499: ldc 'MainActivity'
    //   501: aload #10
    //   503: invokevirtual toString : ()Ljava/lang/String;
    //   506: aload #9
    //   508: invokestatic e : (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I
    //   511: pop
    //   512: iload_3
    //   513: istore_2
    //   514: goto -> 141
    //   517: iload_2
    //   518: istore_1
    //   519: iconst_2
    //   520: anewarray java/lang/String
    //   523: dup
    //   524: iconst_0
    //   525: ldc_w 'com.isvisoft.flutter_screen_recording.ScreenRecordingService'
    //   528: aastore
    //   529: dup
    //   530: iconst_1
    //   531: ldc_w 'com.foregroundservice.ForegroundService'
    //   534: aastore
    //   535: invokestatic k : ([Ljava/lang/Object;)Ljava/util/List;
    //   538: invokeinterface iterator : ()Ljava/util/Iterator;
    //   543: astore #5
    //   545: iload_2
    //   546: istore_1
    //   547: aload #5
    //   549: invokeinterface hasNext : ()Z
    //   554: ifeq -> 755
    //   557: iload_2
    //   558: istore_1
    //   559: aload #5
    //   561: invokeinterface next : ()Ljava/lang/Object;
    //   566: checkcast java/lang/String
    //   569: astore #6
    //   571: iload_2
    //   572: istore_3
    //   573: new android/content/Intent
    //   576: astore #9
    //   578: iload_2
    //   579: istore_3
    //   580: aload #9
    //   582: invokespecial <init> : ()V
    //   585: iload_2
    //   586: istore_3
    //   587: new android/content/ComponentName
    //   590: astore #10
    //   592: iload_2
    //   593: istore_3
    //   594: aload #10
    //   596: aload_0
    //   597: aload #6
    //   599: invokespecial <init> : (Landroid/content/Context;Ljava/lang/String;)V
    //   602: iload_2
    //   603: istore_3
    //   604: aload #9
    //   606: aload #10
    //   608: invokevirtual setComponent : (Landroid/content/ComponentName;)Landroid/content/Intent;
    //   611: pop
    //   612: iload_2
    //   613: istore_3
    //   614: aload_0
    //   615: aload #9
    //   617: invokevirtual stopService : (Landroid/content/Intent;)Z
    //   620: ifeq -> 545
    //   623: iinc #2, 1
    //   626: iload_2
    //   627: istore_3
    //   628: aload #8
    //   630: aload #6
    //   632: invokeinterface add : (Ljava/lang/Object;)Z
    //   637: pop
    //   638: iload_2
    //   639: istore_3
    //   640: new java/lang/StringBuilder
    //   643: astore #9
    //   645: iload_2
    //   646: istore_3
    //   647: aload #9
    //   649: invokespecial <init> : ()V
    //   652: iload_2
    //   653: istore_3
    //   654: aload #9
    //   656: ldc_w '✅ 强制停止已知录屏服务: '
    //   659: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   662: pop
    //   663: iload_2
    //   664: istore_3
    //   665: aload #9
    //   667: aload #6
    //   669: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   672: pop
    //   673: iload_2
    //   674: istore_3
    //   675: ldc 'MainActivity'
    //   677: aload #9
    //   679: invokevirtual toString : ()Ljava/lang/String;
    //   682: invokestatic i : (Ljava/lang/String;Ljava/lang/String;)I
    //   685: pop
    //   686: goto -> 545
    //   689: astore #9
    //   691: iload_3
    //   692: istore_1
    //   693: new java/lang/StringBuilder
    //   696: astore #9
    //   698: iload_3
    //   699: istore_1
    //   700: aload #9
    //   702: invokespecial <init> : ()V
    //   705: iload_3
    //   706: istore_1
    //   707: aload #9
    //   709: ldc_w '强制停止已知服务失败: '
    //   712: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   715: pop
    //   716: iload_3
    //   717: istore_1
    //   718: aload #9
    //   720: aload #6
    //   722: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   725: pop
    //   726: iload_3
    //   727: istore_1
    //   728: aload #9
    //   730: ldc_w ' (可能未运行)'
    //   733: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   736: pop
    //   737: iload_3
    //   738: istore_1
    //   739: ldc 'MainActivity'
    //   741: aload #9
    //   743: invokevirtual toString : ()Ljava/lang/String;
    //   746: invokestatic d : (Ljava/lang/String;Ljava/lang/String;)I
    //   749: pop
    //   750: iload_3
    //   751: istore_2
    //   752: goto -> 545
    //   755: iload_2
    //   756: istore_1
    //   757: aload #7
    //   759: ldc 'success'
    //   761: getstatic java/lang/Boolean.TRUE : Ljava/lang/Boolean;
    //   764: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   769: pop
    //   770: iload_2
    //   771: istore_1
    //   772: aload #7
    //   774: ldc_w 'stoppedCount'
    //   777: iload_2
    //   778: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   781: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   786: pop
    //   787: iload_2
    //   788: istore_1
    //   789: aload #7
    //   791: ldc_w 'stoppedServices'
    //   794: aload #8
    //   796: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   801: pop
    //   802: iload_2
    //   803: istore_1
    //   804: new java/lang/StringBuilder
    //   807: astore #5
    //   809: iload_2
    //   810: istore_1
    //   811: aload #5
    //   813: invokespecial <init> : ()V
    //   816: iload_2
    //   817: istore_1
    //   818: aload #5
    //   820: ldc_w '🎯 前台服务清理完成，共停止 '
    //   823: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   826: pop
    //   827: iload_2
    //   828: istore_1
    //   829: aload #5
    //   831: iload_2
    //   832: invokevirtual append : (I)Ljava/lang/StringBuilder;
    //   835: pop
    //   836: iload_2
    //   837: istore_1
    //   838: aload #5
    //   840: ldc_w ' 个服务'
    //   843: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
    //   846: pop
    //   847: iload_2
    //   848: istore_1
    //   849: ldc 'MainActivity'
    //   851: aload #5
    //   853: invokevirtual toString : ()Ljava/lang/String;
    //   856: invokestatic i : (Ljava/lang/String;Ljava/lang/String;)I
    //   859: pop
    //   860: goto -> 951
    //   863: astore #5
    //   865: ldc 'MainActivity'
    //   867: ldc_w '清理前台服务时发生错误'
    //   870: aload #5
    //   872: invokestatic e : (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I
    //   875: pop
    //   876: aload #7
    //   878: ldc 'success'
    //   880: getstatic java/lang/Boolean.FALSE : Ljava/lang/Boolean;
    //   883: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   888: pop
    //   889: aload #5
    //   891: invokevirtual getMessage : ()Ljava/lang/String;
    //   894: astore #6
    //   896: aload #6
    //   898: astore #5
    //   900: aload #6
    //   902: ifnonnull -> 910
    //   905: ldc_w '未知错误'
    //   908: astore #5
    //   910: aload #7
    //   912: ldc_w 'error'
    //   915: aload #5
    //   917: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   922: pop
    //   923: aload #7
    //   925: ldc_w 'stoppedCount'
    //   928: iload_1
    //   929: invokestatic valueOf : (I)Ljava/lang/Integer;
    //   932: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   937: pop
    //   938: aload #7
    //   940: ldc_w 'stoppedServices'
    //   943: aload #8
    //   945: invokeinterface put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    //   950: pop
    //   951: aload #7
    //   953: areturn
    // Exception table:
    //   from	to	target	type
    //   24	33	863	java/lang/Exception
    //   35	43	863	java/lang/Exception
    //   45	52	863	java/lang/Exception
    //   54	66	863	java/lang/Exception
    //   68	73	863	java/lang/Exception
    //   75	80	863	java/lang/Exception
    //   82	91	863	java/lang/Exception
    //   93	106	863	java/lang/Exception
    //   108	117	863	java/lang/Exception
    //   119	130	863	java/lang/Exception
    //   132	141	863	java/lang/Exception
    //   143	153	863	java/lang/Exception
    //   155	167	863	java/lang/Exception
    //   169	187	863	java/lang/Exception
    //   189	199	863	java/lang/Exception
    //   201	208	863	java/lang/Exception
    //   210	215	863	java/lang/Exception
    //   217	222	863	java/lang/Exception
    //   224	233	863	java/lang/Exception
    //   235	243	863	java/lang/Exception
    //   245	254	863	java/lang/Exception
    //   256	267	863	java/lang/Exception
    //   269	280	863	java/lang/Exception
    //   282	290	863	java/lang/Exception
    //   292	300	863	java/lang/Exception
    //   307	312	460	java/lang/Exception
    //   314	319	460	java/lang/Exception
    //   321	332	460	java/lang/Exception
    //   334	343	460	java/lang/Exception
    //   348	358	460	java/lang/Exception
    //   360	365	460	java/lang/Exception
    //   367	372	460	java/lang/Exception
    //   374	383	460	java/lang/Exception
    //   385	393	460	java/lang/Exception
    //   395	406	460	java/lang/Exception
    //   411	416	460	java/lang/Exception
    //   418	423	460	java/lang/Exception
    //   425	434	460	java/lang/Exception
    //   436	444	460	java/lang/Exception
    //   446	457	460	java/lang/Exception
    //   464	469	863	java/lang/Exception
    //   471	476	863	java/lang/Exception
    //   478	487	863	java/lang/Exception
    //   489	497	863	java/lang/Exception
    //   499	512	863	java/lang/Exception
    //   519	545	863	java/lang/Exception
    //   547	557	863	java/lang/Exception
    //   559	571	863	java/lang/Exception
    //   573	578	689	java/lang/Exception
    //   580	585	689	java/lang/Exception
    //   587	592	689	java/lang/Exception
    //   594	602	689	java/lang/Exception
    //   604	612	689	java/lang/Exception
    //   614	623	689	java/lang/Exception
    //   628	638	689	java/lang/Exception
    //   640	645	689	java/lang/Exception
    //   647	652	689	java/lang/Exception
    //   654	663	689	java/lang/Exception
    //   665	673	689	java/lang/Exception
    //   675	686	689	java/lang/Exception
    //   693	698	863	java/lang/Exception
    //   700	705	863	java/lang/Exception
    //   707	716	863	java/lang/Exception
    //   718	726	863	java/lang/Exception
    //   728	737	863	java/lang/Exception
    //   739	750	863	java/lang/Exception
    //   757	770	863	java/lang/Exception
    //   772	787	863	java/lang/Exception
    //   789	802	863	java/lang/Exception
    //   804	809	863	java/lang/Exception
    //   811	816	863	java/lang/Exception
    //   818	827	863	java/lang/Exception
    //   829	836	863	java/lang/Exception
    //   838	847	863	java/lang/Exception
    //   849	860	863	java/lang/Exception
  }
  
  public void onCreate(Bundle paramBundle) {
    super.onCreate(paramBundle);
    i0(getWindow());
    f0(getIntent());
  }
  
  public void onNewIntent(Intent paramIntent) {
    m.e(paramIntent, "intent");
    super.onNewIntent(paramIntent);
    f0(paramIntent);
  }
  
  public void onPause() {
    super.onPause();
    io.flutter.embedding.engine.a a1 = N();
    if (a1 != null) {
      ze.a a2 = a1.k();
      if (a2 != null) {
        b b = a2.k();
        if (b != null)
          (new j(b, "app_lifecycle")).c("onPause", null); 
      } 
    } 
  }
  
  public void onResume() {
    super.onResume();
    io.flutter.embedding.engine.a a1 = N();
    if (a1 != null) {
      ze.a a2 = a1.k();
      if (a2 != null) {
        b b = a2.k();
        if (b != null)
          (new j(b, "app_lifecycle")).c("onResume", null); 
      } 
    } 
  }
  
  public void s(io.flutter.embedding.engine.a parama) {
    m.e(parama, "flutterEngine");
    super.s(parama);
    parama.r().j((df.a)new PoseLandmarkerPlugin());
    (new j(parama.k().k(), "screen_recorder")).e((j.c)new wd.a(this));
    j j2 = new j(parama.k().k(), "com.jxhy.partyr/file_import");
    this.f = j2;
    j2.e((j.c)new b());
    j j1 = new j(parama.k().k(), "com.jxhy.partyr/recording_lifecycle");
    this.g = j1;
    j1.e((j.c)new c(this));
    s7.a.a a1 = s7.a.a;
    String str = a1.d();
    if (str != null) {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("发现待处理的导入文件: ");
      stringBuilder.append(str);
      Log.i("MainActivity", stringBuilder.toString());
      j j3 = this.f;
      if (j3 != null)
        j3.c("importFile", a0.b(l.a("filePath", str))); 
      a1.a();
    } 
  }
  
  public static final class a {
    public a() {}
  }
}


/* Location:              E:\ai-dance\tiaotiaodashi\tiaotiao.jar!\com\jxhy\partyr\MainActivity.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */