# AI跳舞功能源代码流程及映射表

## 1. 摄像头动作捕捉

### 摄像头数据获取
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：175-314
映射：main-final.java line：77-200 (ImageConverter类)

### YUV格式转换
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：151-173
映射：main-final.java line：95-130 (convertYUVToRGB方法)

### RGBA格式转换  
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：63-71
映射：main-final.java line：132-150 (convertRGBAToARGB方法)

### 图像变换处理
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：629-700
映射：main-final.java line：152-200 (ImageTransformer类)

### 设备方向获取
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：101-130
映射：main-final.java line：201-230 (DeviceOrientationHelper类)

## 2. 姿态检测初始化

### MediaPipe模型加载
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：433-521
映射：main-final.java line：232-275 (AssetManager类)

### 模型文件缓存
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：73-99
映射：main-final.java line：260-275 (copyAssetToCache方法)

### PoseLandmarker配置
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarker.java line：76-306
映射：main-final.java line：855-870 (主程序初始化部分)

### GPU加速设置
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：454-455
映射：main-final.java line：453-454 (BaseOptions.builder().setDelegate(Delegate.GPU))

## 3. 实时姿态检测

### 异步检测调用
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarker.java line：172-178
映射：main-final.java line：263 (poseLandmarker.detectAsync)

### 检测结果处理
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：410-431
映射：main-final.java line：697-760 (PoseResultHandler类)

### 关键点数据提取
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：418-420
映射：main-final.java line：715-730 (handlePoseResult方法)

### 33个关键点结构
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarkerResult.java line：24-36
映射：main-final.java line：23-35 (PoseLandmark类)

## 4. 姿态连接关系

### 骨骼连接定义
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarksConnections.java line：10-14
映射：main-final.java line：320-365 (POSE_CONNECTIONS数组)

### 连接关系查询
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarksConnections.java line：全文
映射：main-final.java line：367-380 (getConnectedLandmarks方法)

## 5. 动作相似度计算

### 余弦相似度核心算法
文件：com/google/mediapipe/tasks/components/utils/CosineSimilarity.java line：14-35
映射：main-final.java line：470-508 (computeFloat方法)

### 特征向量转换
文件：无直接对应（算法推导）
映射：main-final.java line：415-435 (poseToVector方法)

### 点积和模长计算
文件：com/google/mediapipe/tasks/components/utils/CosineSimilarity.java line：22-31
映射：main-final.java line：445-470 (computeCosineSimilarity方法)

### 姿态相似度计算
文件：无直接对应（业务逻辑）
映射：main-final.java line：395-413 (calculatePoseSimilarity方法)

## 6. 舞蹈评分系统

### 整体评分计算
文件：无直接对应（业务逻辑）
映射：main-final.java line：530-570 (evaluatePerformance方法)

### 流畅度分析
文件：无直接对应（算法推导）
映射：main-final.java line：590-620 (calculateSmoothness方法)

### 节奏稳定性分析
文件：无直接对应（算法推导）
映射：main-final.java line：625-650 (analyzeRhythm方法)

### 评分等级判定
文件：无直接对应（业务逻辑）
映射：main-final.java line：575-588 (getGradeByScore方法)

## 7. 动作纠正建议

### 身体部位差异分析
文件：无直接对应（业务逻辑）
映射：main-final.java line：800-853 (MotionCorrectionSystem类)

### 头部动作分析
文件：无直接对应（算法推导）
映射：main-final.java line：775-780 (HEAD_POINTS定义)

### 手臂动作分析
文件：无直接对应（算法推导）
映射：main-final.java line：775-780 (ARM_POINTS定义)

### 腿部动作分析
文件：无直接对应（算法推导）
映射：main-final.java line：775-780 (LEG_POINTS定义)

### 3D欧几里得距离计算
文件：无直接对应（数学公式）
映射：main-final.java line：835-845 (calculatePartDifference方法)

## 8. 数据结构定义

### 姿态关键点结构
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarkerResult.java
映射：main-final.java line：23-35 (PoseLandmark类)

### 完整姿态数据
文件：无直接对应（数据封装）
映射：main-final.java line：44-65 (PoseData类)

### 舞蹈序列数据
文件：无直接对应（业务封装）
映射：main-final.java line：67-75 (DanceSequence类)

### 评分结果结构
文件：无直接对应（业务封装）
映射：main-final.java line：660-675 (DanceScore类)

## 9. 图像处理工具

### Bitmap变换矩阵
文件：ke/a.java line：140-150
映射：main-final.java line：170-195 (transformBitmap方法)

### 图像旋转处理
文件：c7/c0.java line：88-141
映射：main-final.java line：185-200 (calculateRotationAngle方法)

### 颜色空间转换
文件：androidx/camera/core/ImageProcessingUtil.java line：104-131
映射：main-final.java line：95-130 (YUV转RGB算法)

## 10. 系统集成

### Flutter通道通信
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：523-550
映射：main-final.java line：132-142 (handleResult中的通道调用)

### 异常处理机制
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：168-173
映射：main-final.java line：120-125 (try-catch异常处理)

### 内存管理
文件：ke/a.java line：146-148
映射：main-final.java line：190-195 (OutOfMemoryError处理)

### 日志记录
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：437
映射：main-final.java line：310 (Log.e调用)

## 11. 性能优化

### GPU加速配置
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：454
映射：main-final.java line：454 (Delegate.GPU设置)

### 异步处理机制
文件：com/google/mediapipe/tasks/vision/poselandmarker/PoseLandmarker.java line：172-178
映射：main-final.java line：263 (detectAsync调用)

### 缓存机制
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：80-87
映射：main-final.java line：265-270 (文件缓存检查)

### 内存优化
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：254-256
映射：main-final.java line：115-120 (数组复用)

## 12. 主程序流程

### 初始化流程
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：433-521
映射：main-final.java line：855-870 (main方法初始化)

### 检测循环
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：175-314
映射：main-final.java line：880-920 (舞蹈序列创建)

### 结果处理
文件：com/jxhy/partr/PoseLandmarkerPlugin.java line：410-431
映射：main-final.java line：930-970 (评分计算)

### 反馈输出
文件：无直接对应（UI逻辑）
映射：main-final.java line：975-1002 (结果展示)

## 总结

本映射表涵盖了AI跳舞功能的完整实现链路，从底层的图像处理、姿态检测，到上层的动作比对、评分算法，每个功能模块都有明确的源文件对应关系。main-final.java文件成功整合了所有核心功能，实现了完整的AI跳舞系统。
